use emmylua_parser::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, LuaDocTagCast};
use rowan::{NodeOrToken, TextRange};

use crate::diagnostic::checker::generic::infer_doc_type::infer_doc_type;
use crate::{
    DiagnosticCode, LuaDeclId, LuaType, SemanticDeclLevel, SemanticModel, TypeCheckFailReason,
    TypeCheckResult,
};

use super::{humanize_lint_type, Checker, DiagnosticContext};

pub struct CastTypeMismatchChecker;

impl Checker for CastTypeMismatchChecker {
    const CODES: &[DiagnosticCode] = &[DiagnosticCode::CastTypeMismatch];

    fn check(context: &mut DiagnosticContext, semantic_model: &SemanticModel) {
        for node in semantic_model.get_root().descendants::<LuaAst>() {
            if let LuaAst::LuaDocTagCast(cast_tag) = node {
                check_cast_tag(context, semantic_model, &cast_tag);
            }
        }
    }
}

fn check_cast_tag(
    context: &mut DiagnosticContext,
    semantic_model: &SemanticModel,
    cast_tag: &LuaDocTagCast,
) -> Option<()> {
    // 获取变量名
    let var_name_token = cast_tag.get_name_token()?;
    let var_name = var_name_token.get_name_text();

    // 查找变量声明
    let var_decl_id = find_variable_declaration(semantic_model, &var_name, cast_tag.get_range())?;
    // 获取变量的原始类型
    let original_type = get_variable_type(semantic_model, &var_decl_id)?;

    // 检查每个 cast 操作类型
    for op_type in cast_tag.get_op_types() {
        if let Some(target_doc_type) = op_type.get_type() {
            // 推断目标类型
            let target_type = infer_doc_type(semantic_model, &target_doc_type);

            // 检查类型转换是否有效
            check_cast_compatibility(
                context,
                semantic_model,
                op_type.get_range(),
                &original_type,
                &target_type,
            );
        }
    }

    Some(())
}

fn find_variable_declaration(
    semantic_model: &SemanticModel,
    var_name: &str,
    _cast_range: TextRange,
) -> Option<LuaDeclId> {
    // 简化实现：直接查找当前文件中的所有声明
    let file_id = semantic_model.get_file_id();
    let decl_index = semantic_model.get_db().get_decl_index();
    let decl_tree = decl_index.get_decl_tree(&file_id)?;

    // 遍历所有声明，查找匹配的变量名
    for (decl_id, decl) in decl_tree.get_decls() {
        if decl.get_name() == var_name && decl.is_local() {
            return Some(*decl_id);
        }
    }

    None
}

fn get_variable_type(semantic_model: &SemanticModel, decl_id: &LuaDeclId) -> Option<LuaType> {
    let type_cache = semantic_model
        .get_db()
        .get_type_index()
        .get_type_cache(&(*decl_id).into())?;
    Some(type_cache.as_type().clone())
}

fn check_cast_compatibility(
    context: &mut DiagnosticContext,
    semantic_model: &SemanticModel,
    range: TextRange,
    original_type: &LuaType,
    target_type: &LuaType,
) -> Option<()> {
    // 如果类型相同，则无需检查
    if original_type == target_type {
        return Some(());
    }

    // 检查是否可以从原始类型转换为目标类型
    let result = can_cast_type(semantic_model, original_type, target_type);

    if !result.is_ok() {
        add_cast_type_mismatch_diagnostic(
            context,
            semantic_model,
            range,
            original_type,
            target_type,
            result,
        );
    }

    Some(())
}

fn can_cast_type(
    semantic_model: &SemanticModel,
    original_type: &LuaType,
    target_type: &LuaType,
) -> TypeCheckResult {
    if let LuaType::Union(union_type) = original_type {
        for member_type in union_type.get_types() {
            if semantic_model.type_check(target_type, member_type).is_ok() {
                return Ok(());
            }
        }
        return Err(TypeCheckFailReason::TypeNotMatch);
    }

    semantic_model.type_check(target_type, original_type)
}

fn add_cast_type_mismatch_diagnostic(
    context: &mut DiagnosticContext,
    semantic_model: &SemanticModel,
    range: TextRange,
    original_type: &LuaType,
    target_type: &LuaType,
    result: TypeCheckResult,
) {
    let db = semantic_model.get_db();
    match result {
        Ok(_) => return,
        Err(reason) => match reason {
            TypeCheckFailReason::TypeNotMatchWithReason(reason) => {
                context.add_diagnostic(
                    DiagnosticCode::CastTypeMismatch,
                    range,
                    t!(
                        "Cannot cast `%{original}` to `%{target}`. %{reason}",
                        original = humanize_lint_type(db, original_type),
                        target = humanize_lint_type(db, target_type),
                        reason = reason
                    )
                    .to_string(),
                    None,
                );
            }
            _ => {
                context.add_diagnostic(
                    DiagnosticCode::CastTypeMismatch,
                    range,
                    t!(
                        "Cannot cast `%{original}` to `%{target}`. %{reason}",
                        original = humanize_lint_type(db, original_type),
                        target = humanize_lint_type(db, target_type),
                        reason = ""
                    )
                    .to_string(),
                    None,
                );
            }
        },
    }
}
